import path from 'path';
import dotenv from 'dotenv';

const envFile = '.env';

dotenv.config({ path: path.resolve(process.cwd(), envFile) });

console.log(`Environment: ${process.env.NODE_ENV}`);
console.log(`Configuración de SMTP: ${process.env.SMTP_HOST}:${process.env.SMTP_PORT}`);


const config = {
  env: process.env.NODE_ENV,
  smtp: {
    host: process.env.SMTP_HOST,
    port: process.env.SMTP_PORT,
    secure: process.env.SMTP_SECURE,
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
    defaultFrom: process.env.SMTP_DEFAULT_FROM,
  }
}

export default config;
