import emailTransporter from '../config/smtpconfig.mjs';

export const sendEmail = async ({ para, asunto, text, html, from }) => {
    try {
        // Construcción dinámica de mailOptions
        const mailOptions = {
            from: from || `"Notificaciones" <${process.env.SMTP_USER}>`,
            to: para,
            subject: asunto,
            ...(text && { text }), 
            ...(html && { html })  
        };

        const result = await emailTransporter.sendMail(mailOptions);
        console.log(`Correo enviado a ${para}: ${result.messageId}`);
        return result;
    } catch (error) {
        console.error('Error al enviar el correo:', error);
        throw error;
    }
};