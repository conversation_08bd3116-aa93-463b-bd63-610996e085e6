import config from './config.mjs';
import nodemailer from 'nodemailer';

const emailTransporter = nodemailer.createTransport({
  host: config.smtp.host, // Servidor SMTP de Office 365 (ej: 'smtp.office365.com')
  port: config.smtp.port, // Puerto
  secure: false, // STARTTLS, cifrado se inicia después
  auth: {
    user: config.smtp.user, // Correo
    pass: config.smtp.pass, // Contraseña del correo
  },
  tls: {
    ciphers: 'TLSv1.2', // Cifrado de TLSv1.2
  },
});
console.log(emailTransporter)

// Verificar conexión al servidor SMTP
emailTransporter.verify((error, success) => {
  if (error) {
    console.error('Error al conectar con el servidor SMTP:', error);
  } else {
    console.log('Servidor SMTP listo para enviar correos.');
  }
});

export default emailTransporter;