import dotenv from 'dotenv';

dotenv.config();

var recuperarURL = process.env.RECOVERY_URL;
recuperarURL ? recuperarURL : recuperarURL = 'https://staff.rayco.com.py';

export const generatePasswordRecoveryHTML = (correo, token, codigoReseteo) => {
  const recoveryUrl = `${recuperarURL}/recovery_otp?rpwd=${token}`;

  return `
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reseteo de Contraseña - Ray Co.</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
            padding: 10px;
            line-height: 1.4;
        }

        .email-container {
            max-width: 450px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c2c2c 0%, #1a1a1a 100%);
            padding: 15px;
            text-align: center;
            color: white;
        }

        .logo {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 5px;
            letter-spacing: 2px;
            color: #ffffff;
        }

        .header-subtitle {
            font-size: 12px;
            opacity: 0.8;
            font-weight: 300;
        }

        .content {
            padding: 20px 15px;
        }

        .greeting {
            font-size: 16px;
            color: #333;
            margin-bottom: 10px;
            font-weight: 500;
        }

        .message {
            color: #666;
            font-size: 13px;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .otp-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
            margin: 15px 0;
        }

        .otp-label {
            color: #495057;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .otp-code {
            background: #ffffff;
            border: 2px solid #dee2e6;
            border-radius: 4px;
            padding: 12px;
            font-size: 24px;
            font-weight: bold;
            color: #212529;
            letter-spacing: 3px;
            margin: 8px 0;
            font-family: 'Courier New', monospace;
        }

        .otp-instructions {
            color: #6c757d;
            font-size: 11px;
            margin-top: 8px;
        }

        .divider {
            text-align: center;
            margin: 15px 0;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #dee2e6;
        }

        .divider-text {
            background: white;
            padding: 0 12px;
            color: #6c757d;
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .url-section {
            background: #f8f9fa;
            border-radius: 4px;
            padding: 12px;
            margin: 12px 0;
            border-left: 3px solid #6c757d;
        }

        .url-label {
            color: #495057;
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 6px;
        }

        .btn-container {
            text-align: center;
            margin: 12px 0;
        }

        .btn-primary {
            display: inline-block;
            background: #007bff;
            color: white;
            text-decoration: none;
            padding: 10px 25px;
            border-radius: 4px;
            font-weight: 500;
            font-size: 13px;
            transition: background-color 0.2s ease;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .security-notice {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 12px;
            margin: 15px 0;
        }

        .security-title {
            color: #495057;
            font-weight: 500;
            margin-bottom: 6px;
            font-size: 12px;
        }

        .security-text {
            color: #6c757d;
            font-size: 11px;
            line-height: 1.4;
        }

        .footer {
            background: #f8f9fa;
            padding: 12px;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }

        .footer-text {
            color: #6c757d;
            font-size: 11px;
            margin-bottom: 4px;
        }

        .company-info {
            color: #495057;
            font-weight: 500;
            font-size: 12px;
        }

        @media (max-width: 450px) {
            .email-container {
                margin: 5px;
                border-radius: 4px;
            }

            .header, .content {
                padding: 12px;
            }

            .otp-code {
                font-size: 20px;
                letter-spacing: 2px;
            }

            .btn-primary {
                padding: 8px 20px;
                font-size: 12px;
            }

            .logo {
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">RAY CO.</div>
            <div class="header-subtitle">Recuperación de Contraseña</div>
        </div>

        <div class="content">
            <div class="greeting">¡Hola!</div>

            <div class="message">
                Solicitud de restablecimiento de contraseña para <strong>${correo}</strong>.
                Usa el enlace directo.
            </div>

            <div class="url-section">
                <div class="url-label">Enlace Directo:</div>

                <div class="btn-container">
                    <a href="${recoveryUrl}" class="btn-primary">Restablecer Contraseña</a>
                </div>
            </div>

            <div class="security-notice">
                <div class="security-title">Información Importante</div>
                <div class="security-text">
                    • Código válido por 30 minutos<br>
                    • Si no solicitaste este cambio, ignora este correo<br>
                    • No compartas este código con nadie
                </div>
            </div>
        </div>

        <div class="footer">
            <div class="footer-text">
                Correo automático - No responder
            </div>
            <div class="company-info">RAY CO.</div>
        </div>
    </div>
</body>
</html>
  `;
};

export const generatePasswordRecoverySubject = (codigoReseteo) => {
  return `[Ray Co.] Reseteo de Clave: ${codigoReseteo}`;
};
