import { sendEmail } from '../services/smtp.mjs';
import {
  generatePasswordRecoveryHTML,
  generatePasswordRecoverySubject
} from '../templates/recovery.mjs';

export const sendEmailHandler = async (req, res) => {
  try {

    const {
      para = '', // Dirección de correo del destinatario (obligatorio)
      asunto = 'Sin asunto', // Asunto del correo con valor predeterminado
      text, // Texto plano del correo (opcional)
      html // Contenido HTML del correo (opcional)
    } = req.body;
    // Validación
    if (!para.trim()) {
      return res.status(400).json({
        success: false,
        message: 'El campo "para" es obligatorio.'
      });
    }

    // Validación
    if (!text && !html) {
      return res.status(400).json({
        success: false,
        message: 'Debe proporcionar al menos uno de los campos "text" o "html".'
      });
    }

    // Preparar el correo
    const correo = {
      para,
      asunto,
      ...(text && { text }),
      ...(html && { html })
    };
    // Enviar el correo
    const result = await sendEmail(correo);
    res.json({ success: true, message: 'Correo enviado correctamente', data: result });
  } catch (error) {
    console.error('Error en sendEmailHandler:', error);
    res.status(500).json({ success: false, message: 'Error al enviar el correo' });
  }
};

/**
 * Controlador específico para envío de correos de recuperación de contraseña
 * Recibe: correo, token, codigo_reseteo
 */
export const sendRecovery = async (req, res) => {
  try {
    const {
      CORREO = '', // Dirección de correo del destinatario (obligatorio)
      TOKEN = '', // JWT token para generar URL (obligatorio)
      CODIGO_RESETEO = '' // Código OTP para reseteo (obligatorio)
    } = req.body;
    console.log(req.body)
    // Validaciones
    if (!CORREO) {
      console.log('El campo "correo" es obligatorio y debe ser un string válido.');
      return res.status(400).json({
        success: false,
        message: 'El campo "correo" es obligatorio y debe ser un string válido.'
      });
    }

    if (!TOKEN) {
      console.log('El campo "token" es obligatorio y debe ser un string válido.');
      return res.status(400).json({
        success: false,
        message: 'El campo "token" es obligatorio y debe ser un string válido.'
      });
    }

    if (!CODIGO_RESETEO) {
      console.log('El campo "codigo_reseteo" es obligatorio y debe ser un string válido.');
      return res.status(400).json({
        success: false,
        message: 'El campo "codigo_reseteo" es obligatorio y debe ser un string válido.'
      });
    }

    // Generar el HTML y asunto usando la plantilla
    const htmlContent = generatePasswordRecoveryHTML(CORREO, TOKEN, CODIGO_RESETEO);
    const asunto = generatePasswordRecoverySubject(CODIGO_RESETEO);

    // Preparar el correo
    const emailData = {
      para: CORREO,
      asunto: asunto,
      html: htmlContent
    };

    // Enviar el correo
    const result = await sendEmail(emailData);

    res.json({
      success: true,
      message: 'Correo de recuperación enviado correctamente',
      data: {
        ...result,
        destinatario: CORREO,
        codigo_reseteo: CODIGO_RESETEO
      }
    });

  } catch (error) {
    console.error('Error en sendPasswordRecoveryHandler:', error);
    res.status(500).json({
      success: false,
      message: 'Error al enviar el correo de recuperación'
    });
  }
};
