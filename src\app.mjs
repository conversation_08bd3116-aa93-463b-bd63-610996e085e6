import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import http from 'http';
import cookieParser from 'cookie-parser';
import routes from './routes/index.mjs';
import smtpRoutes from './routes/smtp.mjs';
import corsConfig from './config/corsOptionsconfig.mjs';

dotenv.config();

const PORT = process.env.APP_PORT;

const startApiSmtpServer = async () => {
  const app = express();
  const server = http.createServer(app);


  app.use(cors(corsConfig));
  app.use(express.json({ limit: '50mb' }));
  app.use(express.urlencoded({ extended: false }));
  app.use(cookieParser());
  app.use('/smtp', smtpRoutes);
  app.use(routes)

  return new Promise((resolve) => {
    server.listen(PORT, () => {
      resolve();
    });
  })

}

export default startApiSmtpServer
