services:
  smtpservice:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: smtpservice

    # Port Configuration
    ports:
      - "${APP_PORT}:${APP_PORT}"

    # Volume Management 
    volumes:
      - ./src:/app/src
      - ./node_modules:/app/node_modules
      - ./package.json:/app/package.json
      - ./package-lock.json:/app/package-lock.json
      - ./logs:/app/logs

    # Environment Configuration   
    working_dir: /app
    env_file: .env
    restart: unless-stopped
    networks:
      - backend
    command: ["npm", "run", "start"]

# Network Configuration
networks:
  backend:
    external: true