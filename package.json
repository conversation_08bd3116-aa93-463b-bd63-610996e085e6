{"name": "smpt_services", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node ./src/index.mjs", "start:prod": "node ./src/index.mjs", "docker:run": "docker-compose down && docker-compose build --no-cache && docker-compose up -d && docker-compose logs -f", "docker:down": "docker-compose down", "docker:up": "docker-compose up -d", "docker:logs": "docker-compose logs -f", "git:sync": "git pull origin main"}, "license": "MIT", "dependencies": {"cookie-parser": "^1.4.7", "cors": "^2.8.5", "cross-spawn": "^7.0.6", "dotenv": "^17.2.0", "ejs": "^3.1.10", "express": "^5.1.0", "got": "^14.4.7", "http": "^0.0.1-security", "morgan": "^1.10.1", "nodemailer": "^7.0.5", "nodemon": "^3.1.10", "uuid": "^11.1.0"}}